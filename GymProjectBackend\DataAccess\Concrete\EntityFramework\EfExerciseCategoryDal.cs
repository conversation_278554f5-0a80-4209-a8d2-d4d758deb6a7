using Core.DataAccess.EntityFramework;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfExerciseCategoryDal : EfEntityRepositoryBase<ExerciseCategory, GymContext>, IExerciseCategoryDal
    {
        // Constructor injection (DI zorunlu - Scalability için)
        public EfExerciseCategoryDal(GymContext context) : base(context)
        {
        }

        public List<ExerciseCategoryDto> GetAllCategories()
        {
            // DI kullanılıyor - Scalability optimized
            var result = from ec in _context.ExerciseCategories
                         select new ExerciseCategoryDto
                         {
                             ExerciseCategoryID = ec.ExerciseCategoryID,
                             CategoryName = ec.CategoryName,
                             Description = ec.Description,
                             IsActive = ec.IsActive,
                             CreationDate = ec.CreationDate
                         };
            return result.ToList();
        }

        public List<ExerciseCategoryDto> GetActiveCategories()
        {
            // DI kullanılıyor - Scalability optimized
            var result = from ec in _context.ExerciseCategories
                         where ec.IsActive == true
                         orderby ec.CategoryName
                         select new ExerciseCategoryDto
                         {
                             ExerciseCategoryID = ec.ExerciseCategoryID,
                             CategoryName = ec.CategoryName,
                             Description = ec.Description,
                             IsActive = ec.IsActive,
                             CreationDate = ec.CreationDate
                         };
            return result.ToList();
        }

        public ExerciseCategoryDto GetCategoryById(int categoryId)
        {
            // DI kullanılıyor - Scalability optimized
            var result = from ec in _context.ExerciseCategories
                         where ec.ExerciseCategoryID == categoryId
                         select new ExerciseCategoryDto
                         {
                             ExerciseCategoryID = ec.ExerciseCategoryID,
                             CategoryName = ec.CategoryName,
                             Description = ec.Description,
                             IsActive = ec.IsActive,
                             CreationDate = ec.CreationDate
                         };
            return result.FirstOrDefault();
        }

        public IResult AddExerciseCategory(ExerciseCategoryAddDto categoryAddDto)
        {
            try
            {
                var category = new ExerciseCategory
                {
                    CategoryName = categoryAddDto.CategoryName,
                    Description = categoryAddDto.Description,
                    IsActive = true,
                    CreationDate = DateTime.Now
                };

                Add(category);
                return new SuccessResult("Egzersiz kategorisi başarıyla eklendi.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Egzersiz kategorisi eklenirken hata oluştu: {ex.Message}");
            }
        }

        public IResult UpdateExerciseCategory(ExerciseCategoryUpdateDto categoryUpdateDto)
        {
            try
            {
                var existingCategory = Get(c => c.ExerciseCategoryID == categoryUpdateDto.ExerciseCategoryID);
                if (existingCategory == null)
                {
                    return new ErrorResult("Egzersiz kategorisi bulunamadı.");
                }

                existingCategory.CategoryName = categoryUpdateDto.CategoryName;
                existingCategory.Description = categoryUpdateDto.Description;
                existingCategory.IsActive = categoryUpdateDto.IsActive;
                existingCategory.UpdatedDate = DateTime.Now;

                Update(existingCategory);
                return new SuccessResult("Egzersiz kategorisi başarıyla güncellendi.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Egzersiz kategorisi güncellenirken hata oluştu: {ex.Message}");
            }
        }

        public IResult DeleteExerciseCategory(int categoryId)
        {
            try
            {
                var category = Get(c => c.ExerciseCategoryID == categoryId);
                if (category == null)
                {
                    return new ErrorResult("Egzersiz kategorisi bulunamadı.");
                }

                // Soft delete
                category.IsActive = false;
                category.DeletedDate = DateTime.Now;

                Update(category);
                return new SuccessResult("Egzersiz kategorisi başarıyla silindi.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Egzersiz kategorisi silinirken hata oluştu: {ex.Message}");
            }
        }
    }
}
