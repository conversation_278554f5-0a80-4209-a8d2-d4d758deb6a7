using Core.DataAccess.EntityFramework;
using Core.Utilities.Paging;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfProductDal : EfCompanyEntityRepositoryBase<Product, GymContext>, IProductDal
    {
        private readonly Core.Utilities.Security.CompanyContext.ICompanyContext _companyContext;

        // Constructor injection (DI zorunlu - Scalability için)
        public EfProductDal(Core.Utilities.Security.CompanyContext.ICompanyContext companyContext, GymContext context) : base(companyContext, context)
        {
            _companyContext = companyContext;
        }

        public PaginatedResult<Product> GetAllPaginated(ProductPagingParameters parameters)
        {
            // Mevcut kullanıcının şirket ID'sini al
            int companyId = _companyContext.GetCompanyId();

            var query = _context.Products.Where(x => x.IsActive == true && x.CompanyID == companyId);

                // Filtreleme
                if (!string.IsNullOrWhiteSpace(parameters.SearchText))
                {
                    query = query.Where(x => x.Name.Contains(parameters.SearchText));
                }

                if (parameters.MinPrice.HasValue)
                {
                    query = query.Where(x => x.Price >= parameters.MinPrice.Value);
                }

                if (parameters.MaxPrice.HasValue)
                {
                    query = query.Where(x => x.Price <= parameters.MaxPrice.Value);
                }

                if (parameters.IsActive.HasValue)
                {
                    query = query.Where(x => x.IsActive == parameters.IsActive.Value);
                }

                // Sıralama
                switch (parameters.SortBy?.ToLower())
                {
                    case "name":
                        query = parameters.SortDirection?.ToLower() == "asc"
                            ? query.OrderBy(x => x.Name)
                            : query.OrderByDescending(x => x.Name);
                        break;
                    case "price":
                        query = parameters.SortDirection?.ToLower() == "asc"
                            ? query.OrderBy(x => x.Price)
                            : query.OrderByDescending(x => x.Price);
                        break;
                    case "creationdate":
                        query = parameters.SortDirection?.ToLower() == "asc"
                            ? query.OrderBy(x => x.CreationDate)
                            : query.OrderByDescending(x => x.CreationDate);
                        break;
                    default: // ProductID
                        query = parameters.SortDirection?.ToLower() == "asc"
                            ? query.OrderBy(x => x.ProductID)
                            : query.OrderByDescending(x => x.ProductID);
                        break;
                }

                // Sayfalama için toplam kayıt sayısını al
                var totalCount = query.Count();

                // Sayfalama uygula
                var items = query
                    .Skip((parameters.PageNumber - 1) * parameters.PageSize)
                    .Take(parameters.PageSize)
                    .ToList();

                return new PaginatedResult<Product>(items, parameters.PageNumber, parameters.PageSize, totalCount);
        }

        /// <summary>
        /// SOLID prensiplerine uygun: Soft delete işlemi DAL katmanında
        /// </summary>
        public IResult SoftDeleteProduct(int productId, int companyId)
        {
            try
            {
                using (var context = new GymContext())
                {
                    var product = context.Products.FirstOrDefault(p => p.ProductID == productId && p.CompanyID == companyId);
                    if (product == null)
                    {
                        return new ErrorResult("Ürün bulunamadı veya erişim yetkiniz yok.");
                    }

                    product.IsActive = false;
                    product.DeletedDate = DateTime.Now;
                    context.Products.Update(product);
                    context.SaveChanges();

                    return new SuccessResult("Ürün başarıyla silindi.");
                }
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Ürün silinirken hata oluştu: {ex.Message}");
            }
        }

        /// <summary>
        /// SOLID prensiplerine uygun: Update işlemi business logic ile DAL katmanında
        /// </summary>
        public IResult UpdateProductWithBusinessLogic(Product product, int companyId)
        {
            try
            {
                using (var context = new GymContext())
                {
                    var existingProduct = context.Products.FirstOrDefault(p => p.ProductID == product.ProductID && p.CompanyID == companyId);
                    if (existingProduct == null)
                    {
                        return new ErrorResult("Ürün bulunamadı veya erişim yetkiniz yok.");
                    }

                    // Mevcut entity'nin özelliklerini güncelle (Entity tracking sorununu önler)
                    existingProduct.Name = product.Name;
                    existingProduct.Price = product.Price;
                    existingProduct.IsActive = product.IsActive;
                    existingProduct.UpdatedDate = DateTime.Now;
                    // CreationDate ve CompanyID değişmez

                    context.SaveChanges();

                    return new SuccessResult("Ürün başarıyla güncellendi.");
                }
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Ürün güncellenirken hata oluştu: {ex.Message}");
            }
        }

        /// <summary>
        /// SOLID prensiplerine uygun: Validation logic DAL katmanında
        /// </summary>
        public IDataResult<Product> GetProductByIdWithValidation(int productId, int companyId)
        {
            try
            {
                if (_context != null)
                {
                    // DI kullanılıyor - Scalability optimized
                    var product = _context.Products.FirstOrDefault(p => p.ProductID == productId && p.CompanyID == companyId);
                    if (product == null)
                    {
                        return new ErrorDataResult<Product>("Ürün bulunamadı veya erişim yetkiniz yok.");
                    }
                    return new SuccessDataResult<Product>(product);
                }
                else
                {
                    // Backward compatibility
                    using (var context = new GymContext())
                    {
                        var product = context.Products.FirstOrDefault(p => p.ProductID == productId && p.CompanyID == companyId);
                        if (product == null)
                        {
                            return new ErrorDataResult<Product>("Ürün bulunamadı veya erişim yetkiniz yok.");
                        }
                        return new SuccessDataResult<Product>(product);
                    }
                }
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<Product>($"Ürün getirilirken hata oluştu: {ex.Message}");
            }
        }
    }
}
