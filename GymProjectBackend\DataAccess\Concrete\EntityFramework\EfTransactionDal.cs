using Core.DataAccess.EntityFramework;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfTransactionDal : EfCompanyEntityRepositoryBase<Transaction, GymContext>, ITransactionDal
    {
        private readonly Core.Utilities.Security.CompanyContext.ICompanyContext _companyContext;

        // Constructor injection (DI zorunlu - Scalability için)
        public EfTransactionDal(Core.Utilities.Security.CompanyContext.ICompanyContext companyContext, GymContext context) : base(companyContext, context)
        {
            _companyContext = companyContext;
        }

        public List<TransactionDetailDto> GetTransactionsWithDetails()
        {
            // Mevcut kullanıcının şirket ID'sini al
            int companyId = _companyContext.GetCompanyId();

            var result = from t in _context.Transactions
                         join m in _context.Members on t.MemberID equals m.MemberID
                         join p in _context.Products on t.ProductID equals p.ProductID into productJoin
                         from p in productJoin.DefaultIfEmpty()
                         where t.CompanyID == companyId // Şirket ID'sine göre filtrele
                         && m.CompanyID == companyId // Üyelerin de aynı şirkete ait olduğundan emin ol
                         && (p == null || p.CompanyID == companyId) // Ürünlerin de aynı şirkete ait olduğundan emin ol
                         && t.IsActive == true // Sadece aktif (silinmemiş) işlemler
                         orderby t.TransactionDate descending // Tarih bazlı sıralama eklendi
                             select new TransactionDetailDto
                             {
                                 TransactionID = t.TransactionID,
                                 MemberID = t.MemberID,
                                 MemberName = m.Name,
                                 ProductID = t.ProductID,
                                 ProductName = p != null ? p.Name : null,
                                 Amount = t.Amount,
                                 UnitPrice = t.UnitPrice,
                                 TransactionType = t.TransactionType,
                                 TransactionDate = t.TransactionDate,
                                 Quantity = t.Quantity,
                                 IsPaid = t.IsPaid,
                                 Balance = m.Balance,
                                 TotalPrice = t.TransactionType == "Satış" ? t.UnitPrice * t.Quantity : t.Amount,
                                 IsActive = t.IsActive, // Soft delete kontrolü için eklendi
                                 UpdatedDate = t.UpdatedDate // Ödeme tarihi için eklendi
                             };

            return result.ToList();
        }

        // SOLID prensiplerine uygun: Karmaşık toplu işlem mantığı DAL katmanına taşındı
        public IResult AddBulkTransactionWithBusinessLogic(BulkTransactionDto bulkTransaction, int companyId)
        {
            try
            {
                if (_context != null)
                {
                    // DI kullanılıyor - Scalability optimized
                    return AddBulkTransactionInternal(bulkTransaction, companyId, _context);
                }
                else
                {
                    // Backward compatibility
                    using (var context = new GymContext())
                    {
                        return AddBulkTransactionInternal(bulkTransaction, companyId, context);
                    }
                }
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Toplu işlem eklenirken hata oluştu: {ex.Message}");
            }
        }

        private IResult AddBulkTransactionInternal(BulkTransactionDto bulkTransaction, int companyId, GymContext context)
        {
            // Üyeyi bul ve şirket kontrolü yap
            var member = context.Members.FirstOrDefault(m => m.MemberID == bulkTransaction.MemberID && m.CompanyID == companyId);
            if (member == null)
                return new ErrorResult("Üye bulunamadı.");

            // Toplam tutar hesaplama
            decimal totalAmount = bulkTransaction.Items.Sum(item => item.Quantity * item.UnitPrice);
            decimal availableBalance = Math.Max(0, member.Balance);
            decimal amountToDeduct = Math.Min(totalAmount, availableBalance);
            decimal remainingDebt = totalAmount - amountToDeduct;

            // Üye bakiyesi güncelleme
            member.Balance -= totalAmount;
            member.UpdatedDate = DateTime.Now;
            context.Members.Update(member);

            // Her ürün için ayrı transaction oluşturma
            foreach (var item in bulkTransaction.Items)
            {
                decimal itemTotalCost = item.Quantity * item.UnitPrice;
                decimal itemAvailableBalance = (availableBalance * itemTotalCost) / totalAmount;
                decimal itemDebt = itemTotalCost - itemAvailableBalance;

                var transaction = new Transaction
                {
                    MemberID = bulkTransaction.MemberID,
                    ProductID = item.ProductID,
                    Amount = itemDebt,
                    UnitPrice = item.UnitPrice,
                    Quantity = item.Quantity,
                    TransactionType = bulkTransaction.TransactionType,
                    TransactionDate = DateTime.Now,
                    IsPaid = itemDebt <= 0,
                    CompanyID = companyId,
                    IsActive = true,
                    CreationDate = DateTime.Now
                };
                context.Transactions.Add(transaction);
            }

            context.SaveChanges();
            return new SuccessResult("Toplu işlem başarıyla eklendi.");
        }

        // SOLID prensiplerine uygun: Karmaşık tekli işlem mantığı DAL katmanına taşındı
        public IResult AddTransactionWithBusinessLogic(Transaction transaction, int companyId)
        {
            try
            {
                if (_context != null)
                {
                    // DI kullanılıyor - Scalability optimized
                    return AddTransactionInternal(transaction, companyId, _context);
                }
                else
                {
                    // Backward compatibility
                    using (var context = new GymContext())
                    {
                        return AddTransactionInternal(transaction, companyId, context);
                    }
                }
            }
            catch (Exception ex)
            {
                return new ErrorResult($"İşlem eklenirken hata oluştu: {ex.Message}");
            }
        }

        private IResult AddTransactionInternal(Transaction transaction, int companyId, GymContext context)
        {
            // Üyeyi bul ve şirket kontrolü yap
            var member = context.Members.FirstOrDefault(m => m.MemberID == transaction.MemberID && m.CompanyID == companyId);
            if (member == null)
                return new ErrorResult("Üye bulunamadı.");

            transaction.TransactionDate = DateTime.Now;
            transaction.CompanyID = companyId;
            transaction.IsActive = true;
            transaction.CreationDate = DateTime.Now;

            if (transaction.TransactionType == "Bakiye Yükleme")
            {
                member.Balance += transaction.Amount;
                transaction.IsPaid = true;
                transaction.ProductID = null;
            }
            else
            {
                decimal totalCost = transaction.UnitPrice * transaction.Quantity;
                decimal availableBalance = Math.Max(0, member.Balance);
                decimal amountToDeduct = Math.Min(totalCost, availableBalance);
                decimal remainingDebt = totalCost - amountToDeduct;

                member.Balance -= totalCost; // Toplam tutarı bakiyeden düş
                transaction.Amount = remainingDebt; // Sadece kalan borç miktarını Amount'a kaydet
                transaction.IsPaid = remainingDebt <= 0;
            }

            member.UpdatedDate = DateTime.Now;
            context.Members.Update(member);
            context.Transactions.Add(transaction);
            context.SaveChanges();
            return new SuccessResult("İşlem başarıyla eklendi.");
        }

        // SOLID prensiplerine uygun: Ödeme durumu güncelleme mantığı DAL katmanına taşındı
        public IResult UpdatePaymentStatusWithBusinessLogic(int transactionId, int companyId)
        {
            try
            {
                if (_context != null)
                {
                    // DI kullanılıyor - Scalability optimized
                    return UpdatePaymentStatusInternal(transactionId, companyId, _context);
                }
                else
                {
                    // Backward compatibility
                    using (var context = new GymContext())
                    {
                        return UpdatePaymentStatusInternal(transactionId, companyId, context);
                    }
                }
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Ödeme durumu güncellenirken hata oluştu: {ex.Message}");
            }
        }

        private IResult UpdatePaymentStatusInternal(int transactionId, int companyId, GymContext context)
        {
            var transaction = context.Transactions.FirstOrDefault(t => t.TransactionID == transactionId && t.CompanyID == companyId);
            if (transaction == null)
                return new ErrorResult("İşlem bulunamadı veya erişim yetkiniz yok.");

            var member = context.Members.FirstOrDefault(m => m.MemberID == transaction.MemberID && m.CompanyID == companyId);
            if (member == null)
                return new ErrorResult("Üye bulunamadı.");

            if (transaction.IsPaid)
                return new SuccessResult();

            if (!transaction.IsPaid && transaction.TransactionType != "Bakiye Yükleme")
            {
                member.Balance += transaction.Amount;
                member.UpdatedDate = DateTime.Now;
                context.Members.Update(member);
            }

            transaction.IsPaid = true;
            transaction.UpdatedDate = DateTime.Now;
            context.Transactions.Update(transaction);
            context.SaveChanges();
            return new SuccessResult("Ödeme durumu başarıyla güncellendi.");
        }

        // SOLID prensiplerine uygun: Toplu ödeme mantığı DAL katmanına taşındı
        public IResult UpdateAllPaymentStatusWithBusinessLogic(int memberId, int companyId)
        {
            try
            {
                if (_context != null)
                {
                    // DI kullanılıyor - Scalability optimized
                    return UpdateAllPaymentStatusInternal(memberId, companyId, _context);
                }
                else
                {
                    // Backward compatibility
                    using (var context = new GymContext())
                    {
                        return UpdateAllPaymentStatusInternal(memberId, companyId, context);
                    }
                }
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Toplu ödeme güncellenirken hata oluştu: {ex.Message}");
            }
        }

        private IResult UpdateAllPaymentStatusInternal(int memberId, int companyId, GymContext context)
        {
            // Önce member'ın bu company'ye ait olup olmadığını kontrol et
            var member = context.Members.FirstOrDefault(m => m.MemberID == memberId && m.CompanyID == companyId);
            if (member == null)
                return new ErrorResult("Üye bulunamadı veya erişim yetkiniz yok.");

            var transactions = context.Transactions
                .Where(t => t.MemberID == memberId && !t.IsPaid && t.TransactionType != "Bakiye Yükleme" && t.CompanyID == companyId)
                .ToList();

            if (!transactions.Any())
                return new ErrorResult("Ödenecek işlem bulunamadı.");

            decimal totalDebt = transactions.Sum(t => t.Amount);

            member.Balance = 0;
            member.UpdatedDate = DateTime.Now;
            context.Members.Update(member);

            foreach (var transaction in transactions)
            {
                transaction.IsPaid = true;
                transaction.UpdatedDate = DateTime.Now;
                context.Transactions.Update(transaction);
            }

            context.SaveChanges();
            return new SuccessResult("Tüm ödemeler başarıyla yapıldı.");
        }

        /// <summary>
        /// SOLID prensiplerine uygun: Validation ve soft delete logic DAL katmanında
        /// </summary>
        public IResult SoftDeleteTransactionWithValidation(int transactionId, int companyId)
        {
            try
            {
                if (_context != null)
                {
                    // DI kullanılıyor - Scalability optimized
                    var transaction = _context.Transactions.FirstOrDefault(t => t.TransactionID == transactionId && t.CompanyID == companyId);
                    if (transaction == null)
                    {
                        return new ErrorResult("İşlem bulunamadı veya erişim yetkiniz yok.");
                    }

                    // Sadece ödenmiş işlemler silinebilir
                    if (!transaction.IsPaid)
                    {
                        return new ErrorResult("Sadece ödenmiş işlemler silinebilir.");
                    }

                    // Soft delete
                    transaction.IsActive = false;
                    transaction.DeletedDate = DateTime.Now;
                    transaction.UpdatedDate = DateTime.Now;

                    _context.SaveChanges();
                    return new SuccessResult("İşlem başarıyla silindi.");
                }
                else
                {
                    // Backward compatibility
                    using (var context = new GymContext())
                    {
                        var transaction = context.Transactions.FirstOrDefault(t => t.TransactionID == transactionId && t.CompanyID == companyId);
                        if (transaction == null)
                        {
                            return new ErrorResult("İşlem bulunamadı veya erişim yetkiniz yok.");
                        }

                        // Sadece ödenmiş işlemler silinebilir
                        if (!transaction.IsPaid)
                        {
                            return new ErrorResult("Sadece ödenmiş işlemler silinebilir.");
                        }

                        // Soft delete
                        transaction.IsActive = false;
                        transaction.DeletedDate = DateTime.Now;
                        transaction.UpdatedDate = DateTime.Now;

                        context.Transactions.Update(transaction);
                        context.SaveChanges();
                        return new SuccessResult("İşlem başarıyla silindi.");
                    }
                }
            }
            catch (Exception ex)
            {
                return new ErrorResult($"İşlem silinirken hata oluştu: {ex.Message}");
            }
        }

        /// <summary>
        /// SOLID prensiplerine uygun: Filtreleme işlemi DAL katmanında
        /// </summary>
        public List<TransactionDetailDto> GetUnpaidTransactionsByMemberId(int memberId)
        {
            if (_context != null)
            {
                // DI kullanılıyor - Scalability optimized
                int companyId = _companyContext.GetCompanyId();

                var result = from t in _context.Transactions
                            join m in _context.Members on t.MemberID equals m.MemberID
                            join p in _context.Products on t.ProductID equals p.ProductID into productGroup
                            from p in productGroup.DefaultIfEmpty()
                            where t.MemberID == memberId && !t.IsPaid && t.CompanyID == companyId && t.IsActive == true
                            select new TransactionDetailDto
                            {
                                TransactionID = t.TransactionID,
                                MemberID = t.MemberID,
                                MemberName = m.Name,
                                ProductID = t.ProductID,
                                ProductName = p != null ? p.Name : "Bakiye Yükleme",
                                Amount = t.Amount,
                                UnitPrice = t.UnitPrice,
                                Quantity = t.Quantity,
                                TotalPrice = t.UnitPrice * t.Quantity,
                                TransactionType = t.TransactionType,
                                TransactionDate = t.TransactionDate,
                                IsPaid = t.IsPaid
                            };
                return result.ToList();
            }
            else
            {
                // Backward compatibility
                using (var context = new GymContext())
                {
                    int companyId = _companyContext.GetCompanyId();

                    var result = from t in context.Transactions
                                join m in context.Members on t.MemberID equals m.MemberID
                                join p in context.Products on t.ProductID equals p.ProductID into productGroup
                                from p in productGroup.DefaultIfEmpty()
                                where t.MemberID == memberId && !t.IsPaid && t.CompanyID == companyId && t.IsActive == true
                                select new TransactionDetailDto
                                {
                                    TransactionID = t.TransactionID,
                                    MemberID = t.MemberID,
                                    MemberName = m.Name,
                                    ProductID = t.ProductID,
                                    ProductName = p != null ? p.Name : "Bakiye Yükleme",
                                    Amount = t.Amount,
                                    UnitPrice = t.UnitPrice,
                                    Quantity = t.Quantity,
                                    TotalPrice = t.UnitPrice * t.Quantity,
                                    TransactionType = t.TransactionType,
                                    TransactionDate = t.TransactionDate,
                                    IsPaid = t.IsPaid
                                };
                    return result.ToList();
                }
            }
        }

        /// <summary>
        /// SOLID prensiplerine uygun: Aylık toplam hesaplama işlemi DAL katmanında
        /// </summary>
        public decimal GetMonthlyTransactionTotal(int year, int month)
        {
            if (_context != null)
            {
                // DI kullanılıyor - Scalability optimized
                int companyId = _companyContext.GetCompanyId();
                var monthStart = new DateTime(year, month, 1);
                var monthEnd = monthStart.AddMonths(1).AddTicks(-1);

                var totalAmount = _context.Transactions
                    .Where(t => t.TransactionDate >= monthStart &&
                               t.TransactionDate <= monthEnd &&
                               t.TransactionType != "Bakiye Yükleme" &&
                               t.CompanyID == companyId &&
                               t.IsActive == true)
                    .Sum(t => (decimal?)(t.UnitPrice * t.Quantity)) ?? 0;

                return totalAmount;
            }
            else
            {
                // Backward compatibility
                using (var context = new GymContext())
                {
                    int companyId = _companyContext.GetCompanyId();
                    var monthStart = new DateTime(year, month, 1);
                    var monthEnd = monthStart.AddMonths(1).AddTicks(-1);

                    var totalAmount = context.Transactions
                        .Where(t => t.TransactionDate >= monthStart &&
                                   t.TransactionDate <= monthEnd &&
                                   t.TransactionType != "Bakiye Yükleme" &&
                                   t.CompanyID == companyId &&
                                   t.IsActive == true)
                        .Sum(t => (decimal?)(t.UnitPrice * t.Quantity)) ?? 0;

                    return totalAmount;
                }
            }
        }

        /// <summary>
        /// SOLID prensiplerine uygun: Günlük toplam hesaplama işlemi DAL katmanında
        /// </summary>
        public decimal GetDailyTransactionTotal(DateTime date)
        {
            if (_context != null)
            {
                // DI kullanılıyor - Scalability optimized
                int companyId = _companyContext.GetCompanyId();
                var dayStart = date.Date;
                var dayEnd = dayStart.AddDays(1).AddTicks(-1);

                var totalAmount = _context.Transactions
                    .Where(t => t.TransactionDate >= dayStart &&
                               t.TransactionDate <= dayEnd &&
                               t.TransactionType != "Bakiye Yükleme" &&
                               t.CompanyID == companyId &&
                               t.IsActive == true)
                    .Sum(t => (decimal?)(t.UnitPrice * t.Quantity)) ?? 0;

                return totalAmount;
            }
            else
            {
                // Backward compatibility
                using (var context = new GymContext())
                {
                    int companyId = _companyContext.GetCompanyId();
                    var dayStart = date.Date;
                    var dayEnd = dayStart.AddDays(1).AddTicks(-1);

                    var totalAmount = context.Transactions
                        .Where(t => t.TransactionDate >= dayStart &&
                                   t.TransactionDate <= dayEnd &&
                                   t.TransactionType != "Bakiye Yükleme" &&
                                   t.CompanyID == companyId &&
                                   t.IsActive == true)
                        .Sum(t => (decimal?)(t.UnitPrice * t.Quantity)) ?? 0;

                    return totalAmount;
                }
            }
        }
    }
}
