﻿using Core.DataAccess.EntityFramework;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfUserCompanyDal: EfEntityRepositoryBase<UserCompany, GymContext>, IUserCompanyDal
    {
        // Constructor injection (DI zorunlu - Scalability için)
        public EfUserCompanyDal(GymContext context) : base(context)
        {
        }

        public List<UserCompanyDetailDto> GetUserCompanyDetails()
        {
            var result = from uc in _context.UserCompanies
                         join cu in _context.CompanyUsers on uc.UserID equals cu.CompanyUserID
                         join c in _context.Companies on uc.CompanyId equals c.CompanyID
                         where uc.IsActive == true
                         select new UserCompanyDetailDto
                         {
                             UserCompanyId = uc.UserCompanyID,
                             CompanyUserName = cu.Name,
                             CompanyName = c.CompanyName,
                             isActive=uc.IsActive,
                         };
            return result.ToList();
        }

        public int GetUserCompanyId(int userId)
        {
            using (var context = new GymContext())
            {
                // Mevcut veritabanı tasarımına göre: UserCompany.UserID = CompanyUser.CompanyUserID
                // Bu yüzden önce User'ın email'i ile CompanyUser'ı bulup, sonra UserCompany'yi arıyoruz
                var user = context.Users.FirstOrDefault(u => u.UserID == userId);
                if (user == null) return -1;

                var companyUser = context.CompanyUsers.FirstOrDefault(cu => cu.Email == user.Email);
                if (companyUser == null) return -1;

                var userCompany = context.UserCompanies
                    .FirstOrDefault(uc => uc.UserID == companyUser.CompanyUserID && uc.IsActive == true);

                return userCompany?.CompanyId ?? -1;
            }
        }

        public List<UserCompany> GetActiveUserCompanies(int userId)
        {
            return GetAll(uc => uc.UserID == userId && uc.IsActive == true);
        }

        // SOLID prensiplerine uygun: Business logic DAL katmanına taşındı
        public IResult AddUserCompanyWithValidation(UserCompany userCompany)
        {
            try
            {
                using (var context = new GymContext())
                {
                    // Güvenlik kontrolü: UserID'nin gerçek bir User ID'si olduğunu doğrula
                    var user = context.Users.FirstOrDefault(u => u.UserID == userCompany.UserID);
                    if (user == null)
                    {
                        return new ErrorResult("Geçersiz kullanıcı ID'si. Lütfen geçerli bir kullanıcı seçiniz.");
                    }

                    // Entity'yi ekle
                    userCompany.CreationDate = DateTime.Now;
                    userCompany.IsActive = true;

                    context.UserCompanies.Add(userCompany);
                    context.SaveChanges();

                    return new SuccessResult("Kullanıcı şirket ilişkisi başarıyla eklendi.");
                }
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Kullanıcı şirket ilişkisi eklenirken hata oluştu: {ex.Message}");
            }
        }

        public IResult UpdateActiveCompanyWithValidation(int userId, int companyId)
        {
            try
            {
                using (var context = new GymContext())
                {
                    // Kullanıcının tüm şirketlerini getir
                    var userCompanies = context.UserCompanies
                        .Where(uc => uc.UserID == userId)
                        .ToList();

                    // Belirtilen şirketin kullanıcıya ait olup olmadığını kontrol et
                    var targetCompany = userCompanies.FirstOrDefault(uc => uc.CompanyId == companyId);
                    if (targetCompany == null)
                    {
                        return new ErrorResult("Kullanıcı şirket ilişkisi bulunamadı.");
                    }

                    // Şirketin aktif olup olmadığını kontrol et
                    if (targetCompany.IsActive != true)
                    {
                        return new ErrorResult("Şirket aktif değil.");
                    }

                    // Bu noktada validation başarılı - gerçek implementasyonda
                    // aktif şirket güncelleme logic'i burada olacak
                    targetCompany.UpdatedDate = DateTime.Now;
                    context.SaveChanges();

                    return new SuccessResult("Aktif şirket başarıyla güncellendi.");
                }
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Aktif şirket güncellenirken hata oluştu: {ex.Message}");
            }
        }
    }
}
