﻿using Core.DataAccess.EntityFramework;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using Microsoft.EntityFrameworkCore;
using System.Transactions;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfUserLicenseDal : EfEntityRepositoryBase<UserLicense, GymContext>, IUserLicenseDal
    {
        // Constructor injection (DI zorunlu - Scalability için)
        public EfUserLicenseDal(GymContext context) : base(context)
        {
        }

        public List<UserLicenseDto> GetUserLicenseDetails()
        {
            var now = DateTime.Now;
            var result = from ul in _context.UserLicenses
                         join u in _context.Users on ul.UserID equals u.UserID
                         join lp in _context.LicensePackages on ul.LicensePackageID equals lp.LicensePackageID
                         join uc in _context.UserCompanies on u.UserID equals uc.UserID into ucGroup
                         from uc in ucGroup.DefaultIfEmpty()
                         join c in _context.Companies on uc.CompanyId equals c.CompanyID into cGroup
                         from c in cGroup.DefaultIfEmpty()
                         where ul.IsActive && ul.EndDate >= now
                             select new UserLicenseDto
                             {
                                 UserLicenseID = ul.UserLicenseID,
                                 UserID = ul.UserID,
                                 UserName = u.FirstName + " " + u.LastName,
                                 UserEmail = u.Email,
                                 CompanyName = c != null ? c.CompanyName : "Şirket Atanmamış",
                                 LicensePackageID = ul.LicensePackageID,
                                 PackageName = lp.Name,
                                 Role = lp.Role,
                                 StartDate = ul.StartDate,
                                 EndDate = ul.EndDate,
                                 RemainingDays = EF.Functions.DateDiffDay(now, ul.EndDate),
                                 IsActive = ul.IsActive
                             };

            return result.ToList();
        }

        public List<UserLicenseDto> GetActiveUserLicensesByUserId(int userId)
        {
            using (var context = new GymContext())
            {
                var now = DateTime.Now;
                var result = from ul in context.UserLicenses
                             join u in context.Users on ul.UserID equals u.UserID
                             join lp in context.LicensePackages on ul.LicensePackageID equals lp.LicensePackageID
                             join uc in context.UserCompanies on u.UserID equals uc.UserID
                             join c in context.Companies on uc.CompanyId equals c.CompanyID
                             where ul.UserID == userId && ul.IsActive && ul.EndDate >= now
                             select new UserLicenseDto
                             {
                                 UserLicenseID = ul.UserLicenseID,
                                 UserID = ul.UserID,
                                 UserName = u.FirstName + " " + u.LastName,
                                 UserEmail = u.Email,
                                 CompanyName = c.CompanyName,
                                 LicensePackageID = ul.LicensePackageID,
                                 PackageName = lp.Name,
                                 Role = lp.Role,
                                 StartDate = ul.StartDate,
                                 EndDate = ul.EndDate,
                                 RemainingDays = EF.Functions.DateDiffDay(now, ul.EndDate),
                                 IsActive = ul.IsActive
                             };

                return result.ToList();
            }
        }

        public UserLicenseDto GetUserLicenseDetail(int userLicenseId)
        {
            using (var context = new GymContext())
            {
                var now = DateTime.Now;
                var result = from ul in context.UserLicenses
                             join u in context.Users on ul.UserID equals u.UserID
                             join lp in context.LicensePackages on ul.LicensePackageID equals lp.LicensePackageID
                             join uc in context.UserCompanies on u.UserID equals uc.UserID
                             join c in context.Companies on uc.CompanyId equals c.CompanyID
                             where ul.UserLicenseID == userLicenseId
                             select new UserLicenseDto
                             {
                                 UserLicenseID = ul.UserLicenseID,
                                 UserID = ul.UserID,
                                 UserName = u.FirstName + " " + u.LastName,
                                 UserEmail = u.Email,
                                 CompanyName = c.CompanyName,
                                 LicensePackageID = ul.LicensePackageID,
                                 PackageName = lp.Name,
                                 Role = lp.Role,
                                 StartDate = ul.StartDate,
                                 EndDate = ul.EndDate,
                                 RemainingDays = EF.Functions.DateDiffDay(now, ul.EndDate),
                                 IsActive = ul.IsActive
                             };

                return result.FirstOrDefault();
            }
        }

        public PaginatedUserLicenseDto GetUserLicenseDetailsPaginated(int page, int pageSize, string searchTerm, string sortBy, string companyName, int? remainingDaysMin, int? remainingDaysMax)
        {
            using (var context = new GymContext())
            {
                var now = DateTime.Now;
                var query = from ul in context.UserLicenses
                           join u in context.Users on ul.UserID equals u.UserID
                           join lp in context.LicensePackages on ul.LicensePackageID equals lp.LicensePackageID
                           join uc in context.UserCompanies on u.UserID equals uc.UserID into ucGroup
                           from uc in ucGroup.DefaultIfEmpty()
                           join c in context.Companies on uc.CompanyId equals c.CompanyID into cGroup
                           from c in cGroup.DefaultIfEmpty()
                           where ul.IsActive && ul.EndDate >= now
                           select new UserLicenseDto
                           {
                               UserLicenseID = ul.UserLicenseID,
                               UserID = ul.UserID,
                               UserName = u.FirstName + " " + u.LastName,
                               UserEmail = u.Email,
                               CompanyName = c != null ? c.CompanyName : "Şirket Atanmamış",
                               LicensePackageID = ul.LicensePackageID,
                               PackageName = lp.Name,
                               Role = lp.Role,
                               StartDate = ul.StartDate,
                               EndDate = ul.EndDate,
                               RemainingDays = EF.Functions.DateDiffDay(now, ul.EndDate),
                               IsActive = ul.IsActive
                           };

                // Search filtering
                if (!string.IsNullOrEmpty(searchTerm))
                {
                    query = query.Where(x => x.UserEmail.Contains(searchTerm) || x.CompanyName.Contains(searchTerm));
                }

                // Company name filtering
                if (!string.IsNullOrEmpty(companyName))
                {
                    query = query.Where(x => x.CompanyName.Contains(companyName));
                }

                // Remaining days filtering
                if (remainingDaysMin.HasValue)
                {
                    query = query.Where(x => x.RemainingDays >= remainingDaysMin.Value);
                }

                if (remainingDaysMax.HasValue)
                {
                    query = query.Where(x => x.RemainingDays <= remainingDaysMax.Value);
                }

                // Sorting
                switch (sortBy?.ToLower())
                {
                    case "newest":
                        query = query.OrderByDescending(x => x.StartDate);
                        break;
                    case "oldest":
                        query = query.OrderBy(x => x.StartDate);
                        break;
                    case "expiring":
                        query = query.OrderBy(x => x.RemainingDays);
                        break;
                    case "company":
                        query = query.OrderBy(x => x.CompanyName);
                        break;
                    default:
                        query = query.OrderByDescending(x => x.StartDate);
                        break;
                }

                var totalCount = query.Count();
                var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);

                var data = query
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToList();

                return new PaginatedUserLicenseDto
                {
                    Data = data,
                    TotalCount = totalCount,
                    PageNumber = page,
                    PageSize = pageSize,
                    TotalPages = totalPages,
                    HasPreviousPage = page > 1,
                    HasNextPage = page < totalPages
                };
            }
        }

        public PaginatedUserLicenseDto GetExpiredAndPassiveLicenses(int page, int pageSize, string searchTerm)
        {
            using (var context = new GymContext())
            {
                var now = DateTime.Now;
                var query = from ul in context.UserLicenses
                           join u in context.Users on ul.UserID equals u.UserID
                           join lp in context.LicensePackages on ul.LicensePackageID equals lp.LicensePackageID
                           join uc in context.UserCompanies on u.UserID equals uc.UserID into ucGroup
                           from uc in ucGroup.DefaultIfEmpty()
                           join c in context.Companies on uc.CompanyId equals c.CompanyID into cGroup
                           from c in cGroup.DefaultIfEmpty()
                           join cu in context.CompanyUsers on u.UserID equals cu.CompanyUserID into cuGroup
                           from cu in cuGroup.DefaultIfEmpty()
                           join city in context.Cities on cu.CityID equals city.CityID into cityGroup
                           from city in cityGroup.DefaultIfEmpty()
                           join town in context.Towns on cu.TownID equals town.TownID into townGroup
                           from town in townGroup.DefaultIfEmpty()
                           where !ul.IsActive || ul.EndDate < now
                           select new UserLicenseDto
                           {
                               UserLicenseID = ul.UserLicenseID,
                               UserID = ul.UserID,
                               UserName = u.FirstName + " " + u.LastName,
                               UserEmail = u.Email,
                               CompanyName = c != null ? c.CompanyName : "Şirket Atanmamış",
                               LicensePackageID = ul.LicensePackageID,
                               PackageName = lp.Name,
                               Role = lp.Role,
                               StartDate = ul.StartDate,
                               EndDate = ul.EndDate,
                               RemainingDays = EF.Functions.DateDiffDay(now, ul.EndDate),
                               IsActive = ul.IsActive
                           };

                // Comprehensive search filtering
                if (!string.IsNullOrEmpty(searchTerm))
                {
                    var searchLower = searchTerm.ToLower();
                    query = query.Where(x =>
                        x.UserEmail.ToLower().Contains(searchLower) ||
                        x.CompanyName.ToLower().Contains(searchLower) ||
                        x.UserName.ToLower().Contains(searchLower) ||
                        x.PackageName.ToLower().Contains(searchLower) ||
                        x.Role.ToLower().Contains(searchLower)
                    );
                }

                // Order by most recently expired first
                query = query.OrderByDescending(x => x.EndDate);

                var totalCount = query.Count();
                var totalPages = (int)Math.Ceiling((double)totalCount / pageSize);

                var data = query
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToList();

                return new PaginatedUserLicenseDto
                {
                    Data = data,
                    TotalCount = totalCount,
                    PageNumber = page,
                    PageSize = pageSize,
                    TotalPages = totalPages,
                    HasPreviousPage = page > 1,
                    HasNextPage = page < totalPages
                };
            }
        }

        public IResult PurchaseLicense(LicensePurchaseDto licensePurchaseDto)
        {
            using (var scope = new TransactionScope())
            {
                try
                {
                    using (var context = new GymContext())
                    {
                        var user = context.Users.FirstOrDefault(u => u.UserID == licensePurchaseDto.UserID);
                        if (user == null)
                        {
                            return new ErrorResult("Kullanıcı bulunamadı");
                        }

                        var licensePackage = context.LicensePackages.FirstOrDefault(lp => lp.LicensePackageID == licensePurchaseDto.LicensePackageID);
                        if (licensePackage == null)
                        {
                            return new ErrorResult("Lisans paketi bulunamadı");
                        }

                        var now = DateTime.Now;
                        var endDate = now.AddDays(licensePackage.DurationDays);

                        // Aynı role ait aktif lisans var mı diye kontrol et
                        var existingLicenses = context.UserLicenses.Where(ul =>
                            ul.UserID == licensePurchaseDto.UserID &&
                            ul.IsActive &&
                            ul.EndDate > now).ToList();

                        foreach (var existingLicense in existingLicenses)
                        {
                            var existingPackage = context.LicensePackages.FirstOrDefault(lp => lp.LicensePackageID == existingLicense.LicensePackageID);
                            if (existingPackage.Role == licensePackage.Role)
                            {
                                // Aynı rol için lisans zaten var, mevcut lisansı uzatalım
                                var extensionDto = new LicenseExtensionByPackageDto
                                {
                                    UserLicenseId = existingLicense.UserLicenseID,
                                    LicensePackageId = licensePurchaseDto.LicensePackageID,
                                    PaymentMethod = licensePurchaseDto.PaymentMethod
                                };
                                return ExtendLicenseByPackage(extensionDto);
                            }
                        }

                        // Yeni lisans oluştur
                        var userLicense = new UserLicense
                        {
                            UserID = licensePurchaseDto.UserID,
                            LicensePackageID = licensePurchaseDto.LicensePackageID,
                            StartDate = now,
                            EndDate = endDate,
                            IsActive = true,
                            CreationDate = now
                        };
                        context.UserLicenses.Add(userLicense);
                        context.SaveChanges();

                        // Ödeme işlemini kaydet
                        var transaction = new LicenseTransaction
                        {
                            UserID = licensePurchaseDto.UserID,
                            LicensePackageID = licensePurchaseDto.LicensePackageID,
                            UserLicenseID = userLicense.UserLicenseID,
                            Amount = licensePackage.Price,
                            PaymentMethod = licensePurchaseDto.PaymentMethod,
                            TransactionDate = now,
                            IsActive = true,
                            CreationDate = now
                        };
                        context.LicenseTransactions.Add(transaction);
                        context.SaveChanges();

                        scope.Complete();
                        return new SuccessResult("Lisans başarıyla satın alındı");
                    }
                }
                catch (Exception ex)
                {
                    return new ErrorResult($"Lisans satın alınırken bir hata oluştu: {ex.Message}");
                }
            }
        }

        public IResult ExtendLicenseByPackage(LicenseExtensionByPackageDto licenseExtensionByPackageDto)
        {
            using (var context = new GymContext())
            {
                var userLicense = context.UserLicenses.FirstOrDefault(ul => ul.UserLicenseID == licenseExtensionByPackageDto.UserLicenseId);
                if (userLicense == null)
                {
                    return new ErrorResult("Lisans bulunamadı");
                }

                var licensePackage = context.LicensePackages.FirstOrDefault(lp => lp.LicensePackageID == licenseExtensionByPackageDto.LicensePackageId);
                if (licensePackage == null)
                {
                    return new ErrorResult("Lisans paketi bulunamadı");
                }

                // Ödeme yöntemi validasyonu
                if (string.IsNullOrWhiteSpace(licenseExtensionByPackageDto.PaymentMethod))
                {
                    return new ErrorResult("Ödeme yöntemi seçilmelidir");
                }

                using (var scope = new TransactionScope())
                {
                    try
                    {
                        // Lisansı paket süresince uzat
                        var now = DateTime.Now;
                        userLicense.EndDate = userLicense.EndDate > now
                            ? userLicense.EndDate.AddDays(licensePackage.DurationDays)
                            : now.AddDays(licensePackage.DurationDays);
                        userLicense.IsActive = true;
                        userLicense.UpdatedDate = now;
                        context.UserLicenses.Update(userLicense);
                        context.SaveChanges();

                        // Ödeme işlemini kaydet
                        var transaction = new LicenseTransaction
                        {
                            UserID = userLicense.UserID,
                            LicensePackageID = licenseExtensionByPackageDto.LicensePackageId,
                            UserLicenseID = userLicense.UserLicenseID,
                            Amount = licensePackage.Price,
                            PaymentMethod = licenseExtensionByPackageDto.PaymentMethod,
                            TransactionDate = now,
                            IsActive = true,
                            CreationDate = now
                        };
                        context.LicenseTransactions.Add(transaction);
                        context.SaveChanges();

                        scope.Complete();
                        return new SuccessResult($"Lisans {licensePackage.Name} paketi ile başarıyla uzatıldı ({licensePackage.DurationDays} gün)");
                    }
                    catch (Exception ex)
                    {
                        return new ErrorResult($"Lisans uzatılırken bir hata oluştu: {ex.Message}");
                    }
                }
            }
        }

        public List<string> GetUserRoles(int userId)
        {
            using (var context = new GymContext())
            {
                var now = DateTime.Now;
                var activeLicenses = context.UserLicenses.Where(ul =>
                    ul.UserID == userId &&
                    ul.IsActive &&
                    ul.EndDate > now).ToList();

                var roles = new List<string>();

                foreach (var license in activeLicenses)
                {
                    var package = context.LicensePackages.FirstOrDefault(lp => lp.LicensePackageID == license.LicensePackageID);
                    if (package != null && !roles.Contains(package.Role))
                    {
                        roles.Add(package.Role);
                    }
                }

                return roles;
            }
        }

        /// <summary>
        /// SOLID prensiplerine uygun: Validation ve entity manipulation logic DAL katmanında
        /// </summary>
        public IResult RevokeLicenseWithValidation(int userLicenseId)
        {
            try
            {
                if (_context != null)
                {
                    // DI kullanılıyor - Scalability optimized
                    var userLicense = _context.UserLicenses.FirstOrDefault(ul => ul.UserLicenseID == userLicenseId);
                    if (userLicense == null)
                    {
                        return new ErrorResult("Lisans bulunamadı");
                    }

                    userLicense.IsActive = false;
                    userLicense.EndDate = DateTime.Now;
                    userLicense.UpdatedDate = DateTime.Now;

                    _context.SaveChanges();
                    return new SuccessResult("Lisans başarıyla iptal edildi");
                }
                else
                {
                    // Backward compatibility
                    using (var context = new GymContext())
                    {
                        var userLicense = context.UserLicenses.FirstOrDefault(ul => ul.UserLicenseID == userLicenseId);
                        if (userLicense == null)
                        {
                            return new ErrorResult("Lisans bulunamadı");
                        }

                        userLicense.IsActive = false;
                        userLicense.EndDate = DateTime.Now;
                        userLicense.UpdatedDate = DateTime.Now;

                        context.UserLicenses.Update(userLicense);
                        context.SaveChanges();
                        return new SuccessResult("Lisans başarıyla iptal edildi");
                    }
                }
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Lisans iptal edilirken hata oluştu: {ex.Message}");
            }
        }

        /// <summary>
        /// SOLID prensiplerine uygun: Tarih yönetimi DAL katmanında
        /// </summary>
        public IResult AddUserLicenseWithDateManagement(UserLicense userLicense)
        {
            try
            {
                if (_context != null)
                {
                    // DI kullanılıyor - Scalability optimized
                    userLicense.CreationDate = DateTime.Now;
                    userLicense.IsActive = true;

                    _context.UserLicenses.Add(userLicense);
                    _context.SaveChanges();
                    return new SuccessResult("Kullanıcı lisansı başarıyla eklendi");
                }
                else
                {
                    // Backward compatibility
                    using (var context = new GymContext())
                    {
                        userLicense.CreationDate = DateTime.Now;
                        userLicense.IsActive = true;

                        context.UserLicenses.Add(userLicense);
                        context.SaveChanges();
                        return new SuccessResult("Kullanıcı lisansı başarıyla eklendi");
                    }
                }
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Kullanıcı lisansı eklenirken hata oluştu: {ex.Message}");
            }
        }

        /// <summary>
        /// SOLID prensiplerine uygun: Tarih yönetimi DAL katmanında
        /// </summary>
        public IResult UpdateUserLicenseWithDateManagement(UserLicense userLicense)
        {
            try
            {
                if (_context != null)
                {
                    // DI kullanılıyor - Scalability optimized
                    userLicense.UpdatedDate = DateTime.Now;

                    _context.UserLicenses.Update(userLicense);
                    _context.SaveChanges();
                    return new SuccessResult("Kullanıcı lisansı başarıyla güncellendi");
                }
                else
                {
                    // Backward compatibility
                    using (var context = new GymContext())
                    {
                        userLicense.UpdatedDate = DateTime.Now;

                        context.UserLicenses.Update(userLicense);
                        context.SaveChanges();
                        return new SuccessResult("Kullanıcı lisansı başarıyla güncellendi");
                    }
                }
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Kullanıcı lisansı güncellenirken hata oluştu: {ex.Message}");
            }
        }
    }

}
