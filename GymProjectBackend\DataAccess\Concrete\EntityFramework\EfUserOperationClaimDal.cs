﻿using Core.DataAccess.EntityFramework;
using Core.Entities.Concrete;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfUserOperationClaimDal : EfEntityRepositoryBase<UserOperationClaim, GymContext>, IUserOperationClaimDal
    {
        // Constructor injection (DI zorunlu - Scalability için)
        public EfUserOperationClaimDal(GymContext context) : base(context)
        {
        }

        public List<UserOperationClaimDto> GetUserOperationClaimDetails()
        {
            if (_context != null)
            {
                // DI kullanılıyor - Scalability optimized
                var result = from uoc in _context.UserOperationClaims
                             join u in _context.Users on uoc.UserId equals u.UserID
                             join oc in _context.OperationClaims on uoc.OperationClaimId equals oc.OperationClaimId
                             select new UserOperationClaimDto
                             {
                                 UserOperationClaimId = uoc.UserOperationClaimId,
                                 UserId = uoc.UserId,
                                 UserName = u.FirstName + " " + u.LastName,
                                 OperationClaimId = uoc.OperationClaimId,
                                 OperationClaimName = oc.Name,
                                 Email = u.Email
                             };
                return result.ToList();
            }
            else
            {
                // Backward compatibility
                using (GymContext context = new GymContext())
                {
                    var result = from uoc in context.UserOperationClaims
                                 join u in context.Users on uoc.UserId equals u.UserID
                                 join oc in context.OperationClaims on uoc.OperationClaimId equals oc.OperationClaimId
                                 select new UserOperationClaimDto
                                 {
                                     UserOperationClaimId = uoc.UserOperationClaimId,
                                     UserId = uoc.UserId,
                                     UserName = u.FirstName + " " + u.LastName,
                                     OperationClaimId = uoc.OperationClaimId,
                                     OperationClaimName = oc.Name,
                                     Email = u.Email
                                 };
                    return result.ToList();
                }
            }
        }

        public IResult InvalidateUserTokensByUserId(int userId)
        {
            try
            {
                if (_context != null)
                {
                    // DI kullanılıyor - Scalability optimized
                    var userDevices = _context.UserDevices
                        .Where(ud => ud.UserId == userId && ud.IsActive == true)
                        .ToList();

                    foreach (var device in userDevices)
                    {
                        device.RefreshTokenExpiration = DateTime.Now;
                    }

                    _context.SaveChanges();
                }
                else
                {
                    // Backward compatibility
                    using (GymContext context = new GymContext())
                    {
                        var userDevices = context.UserDevices
                            .Where(ud => ud.UserId == userId && ud.IsActive == true)
                            .ToList();

                        foreach (var device in userDevices)
                        {
                            device.RefreshTokenExpiration = DateTime.Now;
                        }

                        context.SaveChanges();
                    }
                }

                return new SuccessResult("Kullanıcı token'ları başarıyla geçersiz kılındı.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Token geçersiz kılma işlemi sırasında hata oluştu: {ex.Message}");
            }
        }
    }
}
