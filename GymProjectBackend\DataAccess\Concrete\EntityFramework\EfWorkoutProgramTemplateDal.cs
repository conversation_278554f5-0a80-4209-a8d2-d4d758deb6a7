using Core.DataAccess.EntityFramework;
using Core.Utilities.Results;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfWorkoutProgramTemplateDal : EfCompanyEntityRepositoryBase<WorkoutProgramTemplate, GymContext>, IWorkoutProgramTemplateDal
    {
        private readonly ICompanyContext _companyContext;

        // Constructor injection (DI zorunlu - Scalability için)
        public EfWorkoutProgramTemplateDal(ICompanyContext companyContext, GymContext context) : base(companyContext, context)
        {
            _companyContext = companyContext;
        }

        public List<WorkoutProgramTemplateListDto> GetWorkoutProgramTemplateList()
        {
            int companyId = _companyContext.GetCompanyId();

            if (_context != null)
            {
                // DI kullanılıyor - Scalability optimized
                var result = from wpt in _context.WorkoutProgramTemplates
                             where wpt.CompanyID == companyId && wpt.IsActive == true
                             select new WorkoutProgramTemplateListDto
                             {
                                 WorkoutProgramTemplateID = wpt.WorkoutProgramTemplateID,
                                 ProgramName = wpt.ProgramName,
                                 Description = wpt.Description,
                                 ExperienceLevel = wpt.ExperienceLevel,
                                 TargetGoal = wpt.TargetGoal,
                                 IsActive = wpt.IsActive,
                                 CreationDate = wpt.CreationDate,
                                 DayCount = _context.WorkoutProgramDays
                                     .Where(d => d.WorkoutProgramTemplateID == wpt.WorkoutProgramTemplateID)
                                     .Count(),
                                 ExerciseCount = _context.WorkoutProgramExercises
                                         .Where(e => _context.WorkoutProgramDays
                                             .Where(d => d.WorkoutProgramTemplateID == wpt.WorkoutProgramTemplateID)
                                             .Select(d => d.WorkoutProgramDayID)
                                             .Contains(e.WorkoutProgramDayID))
                                         .Count()
                                 };

                return result.OrderByDescending(x => x.CreationDate).ToList();
            }
            else
            {
                // Backward compatibility
                using (GymContext context = new GymContext())
                {
                    var result = from wpt in context.WorkoutProgramTemplates
                                 where wpt.CompanyID == companyId && wpt.IsActive == true
                                 select new WorkoutProgramTemplateListDto
                                 {
                                     WorkoutProgramTemplateID = wpt.WorkoutProgramTemplateID,
                                     ProgramName = wpt.ProgramName,
                                     Description = wpt.Description,
                                     ExperienceLevel = wpt.ExperienceLevel,
                                     TargetGoal = wpt.TargetGoal,
                                     IsActive = wpt.IsActive,
                                     CreationDate = wpt.CreationDate,
                                     DayCount = context.WorkoutProgramDays
                                         .Where(d => d.WorkoutProgramTemplateID == wpt.WorkoutProgramTemplateID)
                                         .Count(),
                                     ExerciseCount = context.WorkoutProgramExercises
                                             .Where(e => context.WorkoutProgramDays
                                                 .Where(d => d.WorkoutProgramTemplateID == wpt.WorkoutProgramTemplateID)
                                                 .Select(d => d.WorkoutProgramDayID)
                                                 .Contains(e.WorkoutProgramDayID))
                                             .Count()
                                     };

                    return result.OrderByDescending(x => x.CreationDate).ToList();
                }
            }
        }

        public WorkoutProgramTemplateDto GetWorkoutProgramTemplateDetail(int templateId)
        {
            int companyId = _companyContext.GetCompanyId();

            if (_context != null)
            {
                // DI kullanılıyor - Scalability optimized
                var template = _context.WorkoutProgramTemplates
                    .Where(wpt => wpt.WorkoutProgramTemplateID == templateId && wpt.CompanyID == companyId)
                    .Select(wpt => new WorkoutProgramTemplateDto
                    {
                        WorkoutProgramTemplateID = wpt.WorkoutProgramTemplateID,
                        CompanyID = wpt.CompanyID,
                        ProgramName = wpt.ProgramName,
                        Description = wpt.Description,
                        ExperienceLevel = wpt.ExperienceLevel,
                        TargetGoal = wpt.TargetGoal,
                        IsActive = wpt.IsActive,
                        CreationDate = wpt.CreationDate,
                        DayCount = _context.WorkoutProgramDays
                            .Where(d => d.WorkoutProgramTemplateID == wpt.WorkoutProgramTemplateID)
                            .Count()
                    })
                    .FirstOrDefault();

                if (template != null)
                {
                    // Günleri getir
                    template.Days = GetWorkoutProgramDays(_context, templateId);
                }

                return template;
            }
            else
            {
                // Backward compatibility
                using (GymContext context = new GymContext())
                {
                    var template = context.WorkoutProgramTemplates
                        .Where(wpt => wpt.WorkoutProgramTemplateID == templateId && wpt.CompanyID == companyId)
                        .Select(wpt => new WorkoutProgramTemplateDto
                        {
                            WorkoutProgramTemplateID = wpt.WorkoutProgramTemplateID,
                            CompanyID = wpt.CompanyID,
                            ProgramName = wpt.ProgramName,
                            Description = wpt.Description,
                            ExperienceLevel = wpt.ExperienceLevel,
                            TargetGoal = wpt.TargetGoal,
                            IsActive = wpt.IsActive,
                            CreationDate = wpt.CreationDate,
                            DayCount = context.WorkoutProgramDays
                                .Where(d => d.WorkoutProgramTemplateID == wpt.WorkoutProgramTemplateID)
                                .Count()
                        })
                        .FirstOrDefault();

                    if (template != null)
                    {
                        // Günleri getir
                        template.Days = GetWorkoutProgramDays(context, templateId);
                    }

                    return template;
                }
            }
        }

        private List<WorkoutProgramDayDto> GetWorkoutProgramDays(GymContext context, int templateId)
        {
            var days = context.WorkoutProgramDays
                .Where(d => d.WorkoutProgramTemplateID == templateId)
                .Select(d => new WorkoutProgramDayDto
                {
                    WorkoutProgramDayID = d.WorkoutProgramDayID,
                    WorkoutProgramTemplateID = d.WorkoutProgramTemplateID,
                    DayNumber = d.DayNumber,
                    DayName = d.DayName,
                    IsRestDay = d.IsRestDay,
                    CreationDate = d.CreationDate
                })
                .OrderBy(d => d.DayNumber)
                .ToList();

            // Her gün için egzersizleri getir
            foreach (var day in days)
            {
                day.Exercises = GetWorkoutProgramExercises(context, day.WorkoutProgramDayID);
            }

            return days;
        }

        private List<WorkoutProgramExerciseDto> GetWorkoutProgramExercises(GymContext context, int dayId)
        {
            var exercises = from wpe in context.WorkoutProgramExercises
                           where wpe.WorkoutProgramDayID == dayId
                           select new WorkoutProgramExerciseDto
                           {
                               WorkoutProgramExerciseID = wpe.WorkoutProgramExerciseID,
                               WorkoutProgramDayID = wpe.WorkoutProgramDayID,
                               ExerciseType = wpe.ExerciseType,
                               ExerciseID = wpe.ExerciseID,
                               OrderIndex = wpe.OrderIndex,
                               Sets = wpe.Sets,
                               Reps = wpe.Reps,
                               RestTime = wpe.RestTime,
                               Notes = wpe.Notes,
                               CreationDate = wpe.CreationDate
                           };

            var result = exercises.OrderBy(e => e.OrderIndex).ToList();

            // Egzersiz isimlerini getir
            foreach (var exercise in result)
            {
                if (exercise.ExerciseType == "System")
                {
                    var systemExercise = context.SystemExercises
                        .Where(se => se.SystemExerciseID == exercise.ExerciseID)
                        .Select(se => new { se.ExerciseName, se.Description, CategoryName = context.ExerciseCategories.Where(ec => ec.ExerciseCategoryID == se.ExerciseCategoryID).Select(ec => ec.CategoryName).FirstOrDefault() })
                        .FirstOrDefault();

                    if (systemExercise != null)
                    {
                        exercise.ExerciseName = systemExercise.ExerciseName;
                        exercise.ExerciseDescription = systemExercise.Description;
                        exercise.CategoryName = systemExercise.CategoryName;
                    }
                }
                else if (exercise.ExerciseType == "Company")
                {
                    var companyExercise = context.CompanyExercises
                        .Where(ce => ce.CompanyExerciseID == exercise.ExerciseID)
                        .Select(ce => new { ce.ExerciseName, ce.Description, CategoryName = context.ExerciseCategories.Where(ec => ec.ExerciseCategoryID == ce.ExerciseCategoryID).Select(ec => ec.CategoryName).FirstOrDefault() })
                        .FirstOrDefault();

                    if (companyExercise != null)
                    {
                        exercise.ExerciseName = companyExercise.ExerciseName;
                        exercise.ExerciseDescription = companyExercise.Description;
                        exercise.CategoryName = companyExercise.CategoryName;
                    }
                }
            }

            return result;
        }

        public bool CheckProgramNameExists(string programName, int? excludeId = null)
        {
            int companyId = _companyContext.GetCompanyId();

            if (_context != null)
            {
                // DI kullanılıyor - Scalability optimized
                var query = _context.WorkoutProgramTemplates
                    .Where(t => t.ProgramName == programName && t.IsActive == true && t.CompanyID == companyId);

                if (excludeId.HasValue)
                {
                    query = query.Where(t => t.WorkoutProgramTemplateID != excludeId.Value);
                }

                return query.Any();
            }
            else
            {
                // Backward compatibility
                using (GymContext context = new GymContext())
                {
                    var query = context.WorkoutProgramTemplates
                        .Where(t => t.ProgramName == programName && t.IsActive == true && t.CompanyID == companyId);

                    if (excludeId.HasValue)
                    {
                        query = query.Where(t => t.WorkoutProgramTemplateID != excludeId.Value);
                    }

                    return query.Any();
                }
            }
        }

        public void AddWorkoutProgramWithDaysAndExercises(WorkoutProgramTemplateAddDto templateAddDto, int companyId)
        {
            if (_context != null)
            {
                // DI kullanılıyor - Scalability optimized
                AddWorkoutProgramWithContext(_context, templateAddDto, companyId);
            }
            else
            {
                // Backward compatibility
                using (GymContext context = new GymContext())
                {
                    AddWorkoutProgramWithContext(context, templateAddDto, companyId);
                    context.SaveChanges();
                }
            }
        }

        private void AddWorkoutProgramWithContext(GymContext context, WorkoutProgramTemplateAddDto templateAddDto, int companyId)
        {
            // Ana program şablonunu oluştur
            var template = new WorkoutProgramTemplate
            {
                CompanyID = companyId,
                ProgramName = templateAddDto.ProgramName,
                Description = templateAddDto.Description,
                ExperienceLevel = templateAddDto.ExperienceLevel,
                TargetGoal = templateAddDto.TargetGoal,
                IsActive = true,
                CreationDate = System.DateTime.Now
            };

            context.WorkoutProgramTemplates.Add(template);
            context.SaveChanges(); // ID'yi almak için

            // Günleri ekle
            foreach (var dayDto in templateAddDto.Days)
            {
                var day = new WorkoutProgramDay
                {
                    WorkoutProgramTemplateID = template.WorkoutProgramTemplateID,
                    CompanyID = companyId,
                    DayNumber = dayDto.DayNumber,
                    DayName = dayDto.DayName,
                    IsRestDay = dayDto.IsRestDay,
                    CreationDate = System.DateTime.Now
                };

                context.WorkoutProgramDays.Add(day);
                context.SaveChanges(); // ID'yi almak için

                // Egzersizleri ekle (dinlenme günü değilse)
                if (!dayDto.IsRestDay)
                {
                    foreach (var exerciseDto in dayDto.Exercises)
                    {
                        var exercise = new WorkoutProgramExercise
                        {
                            WorkoutProgramDayID = day.WorkoutProgramDayID,
                            CompanyID = companyId,
                            ExerciseType = exerciseDto.ExerciseType,
                            ExerciseID = exerciseDto.ExerciseID,
                            OrderIndex = exerciseDto.OrderIndex,
                            Sets = exerciseDto.Sets,
                            Reps = exerciseDto.Reps,
                            RestTime = exerciseDto.RestTime,
                            Notes = exerciseDto.Notes,
                            CreationDate = System.DateTime.Now
                        };

                        context.WorkoutProgramExercises.Add(exercise);
                    }
                }
            }
        }

        public void UpdateWorkoutProgramWithDaysAndExercises(WorkoutProgramTemplateUpdateDto templateUpdateDto, int companyId)
        {
            if (_context != null)
            {
                // DI kullanılıyor - Scalability optimized
                UpdateWorkoutProgramWithContext(_context, templateUpdateDto, companyId);
            }
            else
            {
                // Backward compatibility
                using (GymContext context = new GymContext())
                {
                    UpdateWorkoutProgramWithContext(context, templateUpdateDto, companyId);
                    context.SaveChanges();
                }
            }
        }

        private void UpdateWorkoutProgramWithContext(GymContext context, WorkoutProgramTemplateUpdateDto templateUpdateDto, int companyId)
        {
            // TransactionScopeAspect zaten Manager'da var - Ek transaction açmıyoruz
            var templateId = templateUpdateDto.WorkoutProgramTemplateID;
            var now = DateTime.Now;

            // 1. Ana template'i güncelle (tek SQL)
            context.Database.ExecuteSqlInterpolated($@"
                UPDATE WorkoutProgramTemplates
                SET ProgramName = {templateUpdateDto.ProgramName},
                    Description = {templateUpdateDto.Description},
                    ExperienceLevel = {templateUpdateDto.ExperienceLevel},
                    TargetGoal = {templateUpdateDto.TargetGoal},
                    IsActive = {templateUpdateDto.IsActive},
                    UpdatedDate = {now}
                WHERE WorkoutProgramTemplateID = {templateId} AND CompanyID = {companyId}");

            // 2. Eski kayıtları sil (tek SQL - Foreign Key cascade)
            context.Database.ExecuteSqlInterpolated($@"
                DELETE wpe FROM WorkoutProgramExercises wpe
                INNER JOIN WorkoutProgramDays wpd ON wpe.WorkoutProgramDayID = wpd.WorkoutProgramDayID
                WHERE wpd.WorkoutProgramTemplateID = {templateId} AND wpd.CompanyID = {companyId}");

            context.Database.ExecuteSqlInterpolated($@"
                DELETE FROM WorkoutProgramDays
                WHERE WorkoutProgramTemplateID = {templateId} AND CompanyID = {companyId}");

            // 3. Yeni günleri toplu ekle (tek SQL)
            if (templateUpdateDto.Days?.Count > 0)
            {
                var dayValues = string.Join(",", templateUpdateDto.Days.Select((day, index) =>
                    $"({templateId}, {companyId}, {day.DayNumber}, '{day.DayName?.Replace("'", "''")}', {(day.IsRestDay ? 1 : 0)}, '{now:yyyy-MM-dd HH:mm:ss}')"));

                context.Database.ExecuteSqlRaw($@"
                    INSERT INTO WorkoutProgramDays (WorkoutProgramTemplateID, CompanyID, DayNumber, DayName, IsRestDay, CreationDate)
                    VALUES {dayValues}");

                // 4. Egzersizleri toplu ekle (tek SQL)
                var exerciseValues = new List<string>();

                for (int dayIndex = 0; dayIndex < templateUpdateDto.Days.Count; dayIndex++)
                {
                    var dayDto = templateUpdateDto.Days[dayIndex];
                    if (!dayDto.IsRestDay && dayDto.Exercises?.Count > 0)
                    {
                        foreach (var exercise in dayDto.Exercises)
                        {
                            // DayID'yi hesapla (IDENTITY_INSERT kullanmadan)
                            var dayNumber = dayDto.DayNumber;
                            exerciseValues.Add($@"(
                                (SELECT WorkoutProgramDayID FROM WorkoutProgramDays
                                 WHERE WorkoutProgramTemplateID = {templateId} AND DayNumber = {dayNumber}),
                                {companyId},
                                '{exercise.ExerciseType?.Replace("'", "''")}',
                                {exercise.ExerciseID},
                                {exercise.OrderIndex},
                                {exercise.Sets},
                                '{exercise.Reps?.Replace("'", "''")}',
                                {exercise.RestTime ?? 0},
                                '{exercise.Notes?.Replace("'", "''")}',
                                '{now:yyyy-MM-dd HH:mm:ss}'
                            )");
                        }
                    }
                }

                if (exerciseValues.Count > 0)
                {
                    var allExerciseValues = string.Join(",", exerciseValues);
                    context.Database.ExecuteSqlRaw($@"
                        INSERT INTO WorkoutProgramExercises
                        (WorkoutProgramDayID, CompanyID, ExerciseType, ExerciseID, OrderIndex, Sets, Reps, RestTime, Notes, CreationDate)
                        VALUES {allExerciseValues}");
                }
            }
        }

        /// <summary>
        /// SOLID prensiplerine uygun: Validation logic DAL katmanında
        /// </summary>
        public IDataResult<WorkoutProgramTemplate> GetWorkoutProgramTemplateByIdWithValidation(int templateId)
        {
            try
            {
                int companyId = _companyContext.GetCompanyId();

                if (_context != null)
                {
                    // DI kullanılıyor - Scalability optimized
                    var template = _context.WorkoutProgramTemplates
                        .FirstOrDefault(t => t.WorkoutProgramTemplateID == templateId && t.CompanyID == companyId && t.IsActive == true);

                    if (template == null)
                    {
                        return new ErrorDataResult<WorkoutProgramTemplate>("Antrenman programı şablonu bulunamadı");
                    }

                    return new SuccessDataResult<WorkoutProgramTemplate>(template);
                }
                else
                {
                    // Backward compatibility
                    using (var context = new GymContext())
                    {
                        var template = context.WorkoutProgramTemplates
                            .FirstOrDefault(t => t.WorkoutProgramTemplateID == templateId && t.CompanyID == companyId && t.IsActive == true);

                        if (template == null)
                        {
                            return new ErrorDataResult<WorkoutProgramTemplate>("Antrenman programı şablonu bulunamadı");
                        }

                        return new SuccessDataResult<WorkoutProgramTemplate>(template);
                    }
                }
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<WorkoutProgramTemplate>($"Antrenman programı şablonu getirilirken hata oluştu: {ex.Message}");
            }
        }

        /// <summary>
        /// SOLID prensiplerine uygun: Business rules validation ve add işlemi DAL katmanında
        /// </summary>
        public IResult AddWorkoutProgramWithBusinessRules(WorkoutProgramTemplateAddDto templateAddDto, int companyId)
        {
            try
            {
                // Business rules validation
                var validationResult = ValidateWorkoutProgramBusinessRules(templateAddDto.ProgramName, templateAddDto.Days, null);
                if (!validationResult.Success)
                {
                    return validationResult;
                }

                // Complex add operation
                AddWorkoutProgramWithDaysAndExercises(templateAddDto, companyId);
                return new SuccessResult("Antrenman programı başarıyla eklendi.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Antrenman programı eklenirken hata oluştu: {ex.Message}");
            }
        }

        /// <summary>
        /// SOLID prensiplerine uygun: Business rules validation ve update işlemi DAL katmanında
        /// </summary>
        public IResult UpdateWorkoutProgramWithBusinessRules(WorkoutProgramTemplateUpdateDto templateUpdateDto, int companyId)
        {
            try
            {
                // Mevcut programı kontrol et
                var existingTemplate = Get(t => t.WorkoutProgramTemplateID == templateUpdateDto.WorkoutProgramTemplateID && t.CompanyID == companyId);
                if (existingTemplate == null)
                {
                    return new ErrorResult("Antrenman programı bulunamadı.");
                }

                // Business rules validation
                var validationResult = ValidateWorkoutProgramBusinessRules(templateUpdateDto.ProgramName, templateUpdateDto.Days, templateUpdateDto.WorkoutProgramTemplateID);
                if (!validationResult.Success)
                {
                    return validationResult;
                }

                // Complex update operation
                UpdateWorkoutProgramWithDaysAndExercises(templateUpdateDto, companyId);
                return new SuccessResult("Antrenman programı başarıyla güncellendi.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Antrenman programı güncellenirken hata oluştu: {ex.Message}");
            }
        }

        /// <summary>
        /// SOLID prensiplerine uygun: Soft delete validation DAL katmanında
        /// </summary>
        public IResult SoftDeleteWorkoutProgramWithValidation(int templateId, int companyId)
        {
            try
            {
                var template = Get(t => t.WorkoutProgramTemplateID == templateId && t.CompanyID == companyId);
                if (template == null)
                {
                    return new ErrorResult("Antrenman programı bulunamadı.");
                }

                Delete(templateId);
                return new SuccessResult("Antrenman programı başarıyla silindi.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Antrenman programı silinirken hata oluştu: {ex.Message}");
            }
        }

        /// <summary>
        /// SOLID prensiplerine uygun: Business rules validation helper method
        /// </summary>
        private IResult ValidateWorkoutProgramBusinessRules(string programName, List<WorkoutProgramDayAddDto> days, int? excludeId = null)
        {
            // Program adı kontrolü
            bool nameExists = CheckProgramNameExists(programName, excludeId);
            if (nameExists)
            {
                return new ErrorResult("Bu program adı zaten kullanılıyor.");
            }

            // Maksimum gün sayısı kontrolü
            const int MAX_DAYS = 7;
            if (days.Count > MAX_DAYS)
            {
                return new ErrorResult($"Bir antrenman programı en fazla {MAX_DAYS} gün olabilir.");
            }

            // Gün numaralarının kontrolü
            var dayNumbers = days.Select(d => d.DayNumber).ToList();
            if (dayNumbers.Distinct().Count() != dayNumbers.Count)
            {
                return new ErrorResult("Aynı gün numarası birden fazla kez kullanılamaz.");
            }

            if (dayNumbers.Any(d => d < 1 || d > 7))
            {
                return new ErrorResult("Gün numaraları 1-7 arasında olmalıdır.");
            }

            // En az bir antrenman günü kontrolü
            if (!days.Any(d => !d.IsRestDay))
            {
                return new ErrorResult("En az bir antrenman günü olmalıdır.");
            }

            return new SuccessResult();
        }

        /// <summary>
        /// SOLID prensiplerine uygun: Business rules validation helper method for update
        /// </summary>
        private IResult ValidateWorkoutProgramBusinessRules(string programName, List<WorkoutProgramDayUpdateDto> days, int? excludeId = null)
        {
            // Program adı kontrolü
            bool nameExists = CheckProgramNameExists(programName, excludeId);
            if (nameExists)
            {
                return new ErrorResult("Bu program adı zaten kullanılıyor.");
            }

            // Maksimum gün sayısı kontrolü
            const int MAX_DAYS = 7;
            if (days.Count > MAX_DAYS)
            {
                return new ErrorResult($"Bir antrenman programı en fazla {MAX_DAYS} gün olabilir.");
            }

            // Gün numaralarının kontrolü
            var dayNumbers = days.Select(d => d.DayNumber).ToList();
            if (dayNumbers.Distinct().Count() != dayNumbers.Count)
            {
                return new ErrorResult("Aynı gün numarası birden fazla kez kullanılamaz.");
            }

            if (dayNumbers.Any(d => d < 1 || d > 7))
            {
                return new ErrorResult("Gün numaraları 1-7 arasında olmalıdır.");
            }

            // En az bir antrenman günü kontrolü
            if (!days.Any(d => !d.IsRestDay))
            {
                return new ErrorResult("En az bir antrenman günü olmalıdır.");
            }

            return new SuccessResult();
        }
    }
}
